Feature: Change Personal Data Flow
  As a system user
  I want to submit and process change personal data requests
  So that citizen information can be updated

  Background:
    Given the test environment is ready
    
  @smoke
  Scenario: Submit and process a change personal data request
    Given a new change personal data request is submitted via RabbitMQ
    When I wait for the request to be processed by the backend
    Then a "CHANGE_PERSONAL_DATA_CAPTURE" task should be created for the request
    
    When I retrieve the aggregate request data for "CHANGE_PERSONAL_DATA_CAPTURE"
    Then the response should contain the citizen information
    And the task status should be "OPEN"
    
    When I close the "CHANGE_PERSONAL_DATA_CAPTURE" task with valid data
    Then the task should be completed successfully
    And a "VALIDATION_DATA" task should be created
    
  Scenario: Validate data after personal data capture
    Given a completed "CHANGE_PERSONAL_DATA_CAPTURE" task
    When I retrieve the aggregate request data for "VALIDATION_DATA"
    Then the response should contain the updated citizen information
    And the validation rules should be applied
    
    When I close the "VALIDATION_DATA" task
    Then the request should be marked as completed