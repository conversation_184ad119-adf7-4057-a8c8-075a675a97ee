#!/usr/bin/env bash

## Configure bash
# Force abort on errors
set -e

## Change to correct directory
pushd "$(dirname -- "${BASH_SOURCE[0]}")" > /dev/null
trap "popd >/dev/null" TERM EXIT

# source common libraries
# shellcheck source=https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/toolsrc?at=refs%2Fheads%2Fmaster
source <(curl -k -s https://nexusprod.onemrva.priv/repository/bash-libs/bamboo-config/toolsrc)
# shellcheck source=https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/xwikirc?at=refs%2Fheads%2Fmaster
source <(curl -k -s https://nexusprod.onemrva.priv/repository/bash-libs/bamboo-config/xwikirc)
# shellcheck source=https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/apicurio-clirc?at=refs%2Fheads%2Fmaster
source <(curl -k -s https://nexusprod.onemrva.priv/repository/bash-libs/bamboo-config/apicurio-clirc)
# shellcheck source=https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/structurizrrc?at=refs%2Fheads%2Fmaster
source <(curl -k -s https://nexusprod.onemrva.priv/repository/bash-libs/bamboo-config/structurizrrc)
# shellcheck source=https://bitbucket.onemrva.priv/projects/ARCHI/repos/bamboo-config/raw/tools/gitversionrc?at=refs%2Fheads%2Fmaster
source <(curl -k -s https://nexusprod.onemrva.priv/repository/bash-libs/bamboo-config/gitversionrc)

ProgName=$(basename "$0")

profile_test(){
  echo "Profile test activated"
  tools__init_docker_ci
  DEFAULT_OPENSHIFT_VERSION=v4
  DEPLOY_HELM_ARGS="--set backend.springConfiguration.spring.datasource.password=${spring_datasource_password}\
   --set kcconfig.keycloak.password=${keycloak_admin_password}\
   --set backend.springConfiguration.spring.security.oauth2.client.registration.keycloak.client-secret=${spring_backend_keycloak_clientsecret}\
   --set bff.springConfiguration.spring.security.oauth2.client.registration.keycloak.client-secret=${spring_bff_keycloak_clientsecret}\
   --set backend.secrets.spring_security_oauth2_client_registration_keycloak_clientsecret=${spring_backend_keycloak_clientsecret}\
   --set bff.secrets.spring_security_oauth2_client_registration_keycloak_clientsecret=${spring_bff_keycloak_clientsecret}"
  DEPLOY_ENV="test"
}

profile_val(){
  echo "Profile val activated"
  tools__init_docker_ci
  DEFAULT_OPENSHIFT_VERSION=v4
  DEPLOY_HELM_ARGS="--set backend.springConfiguration.spring.datasource.password=${spring_datasource_password}\
   --set kcconfig.keycloak.password=${keycloak_admin_password}\
   --set backend.springConfiguration.spring.security.oauth2.client.registration.keycloak.client-secret=${spring_backend_keycloak_clientsecret}\
   --set bff.springConfiguration.spring.security.oauth2.client.registration.keycloak.client-secret=${spring_bff_keycloak_clientsecret}\
   --set backend.secrets.spring_security_oauth2_client_registration_keycloak_clientsecret=${spring_backend_keycloak_clientsecret}\
   --set bff.secrets.spring_security_oauth2_client_registration_keycloak_clientsecret=${spring_bff_keycloak_clientsecret}"
  DEPLOY_ENV="val"
}

profile_prod(){
  echo "Profile prod activated"
  tools__init_docker_ci
  DEFAULT_OPENSHIFT_VERSION=v4
  DEPLOY_HELM_ARGS="--set backend.secrets.spring_datasource_password=${spring_datasource_password}\
   --set kcconfig.keycloak.password=${keycloak_admin_password}\
   --set backend.secrets.spring_security_oauth2_client_registration_keycloak_clientsecret=${security_oauth2_client_registration_keycloak_clientsecret}\
   --set bff.secrets.spring_security_oauth2_client_registration_keycloak_clientsecret=${spring_backend_keycloak_clientsecret}"
  DEPLOY_ENV="prod"
}

profile_ci(){
  echo "Profile ci activated"
  tools__init_docker_ci
  ns_uid=$(tools__get_first_uid "$(tools__kubecontext_for ci v4)" "$(openshift_namespace $HELM_CHART_NAME ci)")
  DEFAULT_OPENSHIFT_VERSION=v4
  SKAFFOLD_BUILD_OPTS="-p ci --skip-tests --push=false"
  MVN_VERIFY_OPTS="-P sonar"
  DEPLOY_HELM_ARGS="--set infra.keycloak.securityContext.runAsUser=${ns_uid} --set infra.keycloak.podSecurityContext.fsGroup=${ns_uid} \
    --set infra.rabbitmq.podSecurityContext.fsGroup=${ns_uid} --set infra.rabbitmq.containerSecurityContext.runAsUser=${ns_uid}  \
   "
  DEPLOY_ENV="ci"
}

profile_local(){
  echo "Profile local activated"
  SKAFFOLD_BUILD_OPTS="--skip-tests --push=false"
  DEPLOY_ENV="dev"
  # shellcheck disable=SC2034 # Used by apicurio-clirc
  bamboo_apicurio_cli_user_secret_incubator="ysdPkhIdFzspxIWBcNDhXFA1kGf4LcWDg"
  # shellcheck disable=SC2034 # Used by apicurio-clirc
  bamboo_apicurio_cli_keycloak_clientsecret_incubator="8kAw8iZtvg97wIa5orlQNuUjaDwjWKre"
  # shellcheck disable=SC2034 # Used by apicurio-clirc
  bamboo_apicurio_cli_apicurioserver_baseurl_incubator="https://api-registry-incubator.test.paas.onemrva.priv/apis/registry/v2"
  # shellcheck disable=SC2034 # Used by apicurio-clirc
  bamboo_apicurio_cli_keycloak_baseurl_incubator="https://keycloak-incubator.test.paas.onemrva.priv/"
}

init() {
  mkdir -p target
  # shellcheck disable=SC2034 # Used by xwikirc
  XWIKI_ROOT="/spaces/Scrum%20Teams/spaces/U-RRR/spaces/cu"
  SKAFFOLD_BUILD_OPTS=""
  MVN_VERIFY_OPTS=""
  HELM_CHART_NAME="cu"
  JAVA_TOOL_OPTIONS="${JAVA_TOOL_OPTIONS} -Dfile.encoding=UTF8"
  STRUCTURIZR_WORKSPACE_NAME="NEO Unemployment System"
}

package-chart(){
  cp -r helm/ target
  
  update_helm_image_values target/metadata.yaml target/helm/${HELM_CHART_NAME}/values.yaml "onemrva/cu-backend" ".backend.image"
  
  update_helm_image_values target/metadata.yaml target/helm/${HELM_CHART_NAME}/values.yaml "onemrva/cu-e2e-karate" ".karate.image"
  
  update_helm_image_values target/metadata.yaml target/helm/${HELM_CHART_NAME}/values.yaml "onemrva/cu-bff" ".bff.image"
  
  update_helm_image_values target/metadata.yaml target/helm/${HELM_CHART_NAME}/values.yaml "onemrva/cu-e2e-karate" ".karate.image"

  update_helm_image_values target/metadata.yaml target/helm/${HELM_CHART_NAME}/values.yaml "onemrva/cu-cu" ".cu.image"
  
  update_helm_image_values target/metadata.yaml target/helm/${HELM_CHART_NAME}/values.yaml "onemrva/cu-cu-e2e" ".e2e.webcomponent.image"
}

publish_doc() {
  cp README.md docs/structurizr/docs/workspace
  structurizr_cli_push ./docs/structurizr/workspace.dsl
}

generate_changelog(){
  JRELEASER_PREVIOUS_TAG_NAME=$(previous_version_from_git)
  export JRELEASER_PREVIOUS_TAG_NAME
  mvn -B -N jreleaser:changelog
}

set-version(){
    simple_version_from_git
    mvn -B versions:set -D processAllModules -D newVersion="$APP_VERSION"
}

sub_clean(){
  EXIT_STATUS=0
  rm -rf target || EXIT_STATUS=$?
  skaffold delete || EXIT_STATUS=$?
  mvn -B clean || EXIT_STATUS=$?
  exit $EXIT_STATUS
}

sub_help(){
    echo "Usage: $ProgName -[p|--profile <profile>] <subcommand> [options]"
    echo "Subcommands:"
    echo "    clean                      Cleans everything"
    echo "    build                      Compiles everything"
    echo "    test                       Unit test everything"
    echo "    e2e-tests                  Run e2e tests (need previous deployment in the environment)"
    echo "    next-version               Computes and set next version"
    echo "    publish                    Publish built artifacts"
    echo "    release                    Create a release of this system"
    echo "    release-bugfix             Release a bugfix (on a releases branch)"
    echo "    release-beta               To release on a beta server"
    echo "    sonarqube                  Execute Sonarqube analysis"
    echo "    sub                        Directly call a function of this script"
    echo "    deploy                     Deploy on an environment"
    echo "    undeploy                   Undeploy on an environment"
    echo ""
    echo "For help with each subcommand run:"
    echo "$ProgName <subcommand> -h|--help"
    echo ""
}

sub_next-version(){
    set-version
}

sub_publish(){
  publish_doc
  publish_images_to_alpha target/metadata.yaml
  publish_helm_chart target/metadata.yaml target/helm/${HELM_CHART_NAME}
#  mvn deploy -DskipTests
  xwiki__publish_changelog "Current"
  xwiki__publish_md README ./README.md
}

publish_api() {
  echo "Let's publish the api"
  simple_version_from_git
  apicurio-cli ${DEPLOY_ENV} --operation publish --groupid "be.fgov.onerva.${HELM_CHART_NAME}" --artifactid "${HELM_CHART_NAME}-rest-api" --version "$APP_VERSION" --file ./api/public/cu.yaml
}


sub_build(){
  simple_version_from_git
  echo "Let's build !"
  helm dependency build helm/${HELM_CHART_NAME}
  # shellcheck disable=SC2086 # We explicitly want keyword expansion
  skaffold build ${SKAFFOLD_BUILD_OPTS} --file-output=target/images.json
  create_metadata_file target/images.json target/metadata.yaml
  package-chart
  generate_changelog
}

sub_test() {
  echo "Let's unit test all maven modules"
  # shellcheck disable=SC2086 # We explicitly want keyword expansion
  mvn -B clean verify ${MVN_VERIFY_OPTS} "$@"
}

sub_deploy(){
  echo "Deploying application"
  git config --global user.email ${bamboo_ManualBuildTriggerReason_userName}
  # shellcheck disable=SC2086 # We explicitly want keyword expansion
  deploy_on_openshift target/metadata.yaml ${HELM_CHART_NAME} ${DEPLOY_ENV} helm/environments/values-${DEPLOY_ENV}.yaml ${DEPLOY_HELM_ARGS}
  echo "Deploying proxy configuration"
  deploy_proxy_configuration ${HELM_CHART_NAME} ${DEPLOY_ENV}
  if [ ${DEPLOY_ENV} != "ci" ]; then
    publish_api
  fi
}

sub_undeploy() {
  echo "Undeploying"
  helm --kube-context "$(tools__kubecontext_for "${DEPLOY_ENV}")" --namespace "$(openshift_namespace "${HELM_CHART_NAME}" ${DEPLOY_ENV})" uninstall "${HELM_CHART_NAME}"
}

sub_e2e-tests() {
  echo "Running e2e tests"
  helm --kube-context "$(tools__kubecontext_for "${DEPLOY_ENV}")" --namespace "$(openshift_namespace "${HELM_CHART_NAME}" ${DEPLOY_ENV})" test "${HELM_CHART_NAME}" --logs --timeout 20m0s

  extract_backend_coverage backend/target/jacoco-it.exec ${HELM_CHART_NAME} ci backend cu-backend
  
  extract_backend_coverage bff/target/jacoco-it.exec ${HELM_CHART_NAME} ci bff cu-bff
}

sub_sonarqube() {
  curl https://nexusprod.onemrva.priv/repository/e2e-reports/${HELM_CHART_NAME}/coverage/lcov.info -o lcov.info
  curl https://nexusprod.onemrva.priv/repository/e2e-reports/${HELM_CHART_NAME}/jest/lcov.info -o lcov-jest.info
  curl https://nexusprod.onemrva.priv/repository/e2e-reports/${HELM_CHART_NAME}/report.tar.gz -o report.tar.gz

  mvn -B install -DskipTests
  mvn -B org.jacoco:jacoco-maven-plugin:0.8.11:report \
      -Djacoco.dataFile=target/jacoco.exec \
      -f common/pom.xml

  echo "Merging backend coverage"
  merge_coverage backend/target/jacoco.exec backend/target/jacoco-it.exec backend/target/merged.exec
  # Generate XML report for backend from merged file
  mvn -B org.jacoco:jacoco-maven-plugin:0.8.11:report \
      -Djacoco.dataFile=target/merged.exec \
      -f backend/pom.xml

  echo "Merging bff coverage"
  merge_coverage bff/target/jacoco.exec bff/target/jacoco-it.exec bff/target/merged.exec
  # Generate XML report for bff from merged file
  mvn -B org.jacoco:jacoco-maven-plugin:0.8.11:report \
      -Djacoco.dataFile=target/merged.exec \
      -f bff/pom.xml

  mkdir -p coverage/jest coverage/lcov-report
  mv lcov-jest.info coverage/jest/lcov.info
  mv lcov.info coverage/lcov-report/lcov.info

  sonar-scanner
}

sub_sub(){
  echo "Let's call $1"
  SUB=$1
  shift
  $SUB "$@"
}

sub_release(){
    base_release

    simple_version_branch_release
    simple_version_tag
}

sub_release-bugfix(){
  base_release
}

sub_release-beta() {
    promote_images_to_beta target/metadata.yaml
}

sub_fiddling() {
  generate_changelog
}

base_release() {
    set-version
    promote_images_to_beta target/metadata.yaml
    promote_images_to_release target/metadata.yaml
    generate_changelog
    simple_version_from_git
    xwiki__publish_changelog "$APP_VERSION"
}

init

POSITIONAL_ARGS=()

PROFILE="local"
while [[ $# -gt 0 ]]; do
  case $1 in
    -p|--profile)
      PROFILE="$2"
      shift 2 # past argument & value
      ;;
    *)
      POSITIONAL_ARGS+=("$1") # save positional arg
      shift # past argument
      ;;
  esac
done

profile_"${PROFILE}"
if [ $? = 127 ]; then
    echo "Error: '$PROFILE' is not a known profile." >&2
    exit 1
fi


set -- "${POSITIONAL_ARGS[@]}"

subcommand=$1
case $subcommand in
    "" | "-h" | "--help")
        sub_help
        ;;
    *)
        shift
        sub_"${subcommand}" "$@"
        if [ $? = 127 ]; then
            echo "Error: '$subcommand' is not a known subcommand." >&2
            echo "       Run '$ProgName --help' for a list of known subcommands." >&2
            exit 1
        fi
        ;;
esac
