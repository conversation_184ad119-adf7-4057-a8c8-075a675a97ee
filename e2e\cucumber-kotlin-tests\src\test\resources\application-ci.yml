spring:
  profiles:
    active: ci
  
  rabbitmq:
    host: rabbitmq
    port: 5672
    username: guest
    password: guest
    virtual-host: onem<PERSON>

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# CI environment configuration
keycloak:
  auth-server-url: https://cu-ci-kc.test.paas.onemrva.priv
  realm: onemrva-agents

bff:
  base-url: https://cu-ci.test.paas.onemrva.priv

backend:
  base-url: https://cu-ci.test.paas.onemrva.priv

wo-facade:
  base-url: https://wo-configurator-ci.test.paas.onemrva.priv

test:
  user:
    username: cu_user
    password: password
    client-id: cu-frontend

# Logging
logging:
  level:
    root: INFO
    be.fgov.onerva.cu.e2e: DEBUG
    io.ktor.client: DEBUG
    org.springframework.amqp: DEBUG