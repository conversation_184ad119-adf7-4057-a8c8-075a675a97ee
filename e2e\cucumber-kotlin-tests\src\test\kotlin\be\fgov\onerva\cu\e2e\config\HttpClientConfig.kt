package be.fgov.onerva.cu.e2e.config

import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.auth.*
import io.ktor.client.plugins.auth.providers.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.concurrent.atomic.AtomicReference

/**
 * HTTP Client configuration for E2E tests using Ktor client
 * Handles OAuth2 authentication with Keycloak and provides configured HTTP clients
 */
@Configuration
class HttpClientConfig {

    private val logger = LoggerFactory.getLogger(HttpClientConfig::class.java)
    
    @Value("\${keycloak.auth-server-url}")
    private lateinit var keycloakUrl: String
    
    @Value("\${keycloak.realm}")
    private lateinit var realm: String
    
    @Value("\${test.user.username}")
    private lateinit var username: String
    
    @Value("\${test.user.password}")
    private lateinit var password: String
    
    @Value("\${test.user.client-id}")
    private lateinit var clientId: String

    // Thread-safe token storage
    private val currentToken = AtomicReference<TokenResponse?>()

    /**
     * Data class for Keycloak token response
     */
    @Serializable
    data class TokenResponse(
        val access_token: String,
        val expires_in: Int,
        val refresh_expires_in: Int,
        val refresh_token: String,
        val token_type: String,
        val scope: String
    )

    /**
     * Get OAuth2 token from Keycloak using password grant flow
     */
    suspend fun getKeycloakToken(): String {
        val tokenUrl = "$keycloakUrl/realms/$realm/protocol/openid-connect/token"
        
        logger.debug("Requesting token from: $tokenUrl")
        
        val client = HttpClient(CIO) {
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                })
            }
            install(Logging) {
                logger = Logger.DEFAULT
                level = LogLevel.INFO
            }
        }

        try {
            val response: TokenResponse = client.submitForm(
                url = tokenUrl,
                formParameters = parameters {
                    append("grant_type", "password")
                    append("client_id", clientId)
                    append("username", username)
                    append("password", password)
                    append("scope", "openid profile roles onemrva")
                }
            ).body()
            
            currentToken.set(response)
            logger.debug("Successfully obtained token, expires in: ${response.expires_in} seconds")
            return response.access_token
            
        } catch (e: Exception) {
            logger.error("Failed to obtain token from Keycloak", e)
            throw RuntimeException("Authentication failed: ${e.message}", e)
        } finally {
            client.close()
        }
    }

    /**
     * Create configured HTTP client with authentication and JSON support
     */
    @Bean
    fun httpClient(): HttpClient {
        return HttpClient(CIO) {
            // Content negotiation for JSON
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                    prettyPrint = true
                })
            }

            // Authentication with Bearer token
            install(Auth) {
                bearer {
                    loadTokens {
                        runBlocking {
                            val token = getKeycloakToken()
                            BearerTokens(token, "")
                        }
                    }
                    refreshTokens {
                        runBlocking {
                            val token = getKeycloakToken()
                            BearerTokens(token, "")
                        }
                    }
                }
            }

            // Logging for debugging
            install(Logging) {
                logger = Logger.DEFAULT
                level = LogLevel.INFO
                filter { request ->
                    request.url.host.contains("test.paas.onemrva.priv") ||
                    request.url.host.contains("localhost")
                }
            }

            // Request timeout configuration
            install(HttpTimeout) {
                requestTimeoutMillis = 30_000
                connectTimeoutMillis = 10_000
                socketTimeoutMillis = 30_000
            }

            // Default request configuration
            defaultRequest {
                header(HttpHeaders.ContentType, ContentType.Application.Json)
                header(HttpHeaders.Accept, ContentType.Application.Json)
            }
        }
    }

    /**
     * Test authentication by making a simple call
     */
    suspend fun testAuthentication(baseUrl: String): Boolean {
        return try {
            val client = httpClient()
            val response = client.get("$baseUrl/actuator/health")
            val success = response.status.isSuccess()
            logger.info("Authentication test to $baseUrl: ${if (success) "SUCCESS" else "FAILED"} (${response.status})")
            client.close()
            success
        } catch (e: Exception) {
            logger.error("Authentication test failed for $baseUrl", e)
            false
        }
    }
}
