package be.fgov.onerva.cu.backend.adapter.`in`.queue

import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.adapter.mapper.hasEC1
import be.fgov.onerva.cu.backend.adapter.mapper.isChangedOfPersonalDataReady
import be.fgov.onerva.cu.backend.adapter.mapper.isChangedOfPersonalDataTreated
import be.fgov.onerva.cu.backend.adapter.mapper.toChangePersonalDataRequestCommand
import be.fgov.onerva.cu.backend.adapter.mapper.toChangePersonalDataRequestTreatedCommand
import be.fgov.onerva.cu.backend.application.port.`in`.ChangePersonalDataRequestUseCase
import be.fgov.onerva.cu.common.utils.logger
import be.fgov.onerva.rabbitmq.consumer.config.RabbitConsumer
import be.fgov.onerva.unemployment.c9.rest.model.C9

/**
 * Consumer for processing change of address messages received from the C9 message queue.
 *
 * This service listens to a RabbitMQ queue for C9 messages related to address changes. When a valid message
 * is received (type "410" and status "READY_TO_BE_TREATED"), it transforms the message into a [ChangePersonalDataCommand]
 * object and forwards it to the appropriate use case for processing.
 *
 * @property changePersonalDataRequestUseCase Use case for processing received change of address requests
 */
@Service
@RabbitConsumer(exchangeName = "c9.exchange")
@Profile("!ci & !unit")
class C9MessageQueueConsumer(val changePersonalDataRequestUseCase: ChangePersonalDataRequestUseCase) {
    private val log = logger

    /**
     * Processes C9 messages received from the RabbitMQ queue.
     *
     * This method:
     * 1. Logs the receipt of the message
     * 2. Filters for messages of type "410" with status "READY_TO_BE_TREATED"
     * 3. Transforms valid messages into [ChangePersonalDataRequestReceivedCommand] objects
     * 4. Forwards them to the change of address use case for processing
     *
     * @param c9 The C9 message received from the queue containing change of address details
     * @param amqpMessage The raw AMQP message (optional)
     *
     * @see ChangePersonalDataRequestReceivedCommand
     * @see ChangePersonalDataRequestUseCase
     */
    @RabbitListener(queues = ["cu.c9.queue"])
    fun onC9EventReceivedFromQueue(c9: C9, amqpMessage: Message?) {
        try {
            log.info("Message received from C9 id: {} - type: {}.", c9.id, c9.type)

            if (c9.isChangedOfPersonalDataReady()) {
                val changePersonalDataReceived = c9.toChangePersonalDataRequestCommand()
                changePersonalDataRequestUseCase.receivedChangePersonalData(changePersonalDataReceived)
            } else if (c9.isChangedOfPersonalDataTreated()) {
                val changePersonalDataTreated = c9.toChangePersonalDataRequestTreatedCommand()
                changePersonalDataRequestUseCase.processTreatedChangePersonalData(changePersonalDataTreated)
            } else {
                log.warn(
                    "Message ignored from C9 id: {} - type: {} - treatmentStatus: {}, hasEC1: {}, scanUrl: {}.",
                    c9.id,
                    c9.type,
                    c9.treatmentStatus,
                    c9.hasEC1(),
                    c9.scanUrl != null
                )
            }
        } catch (e: Exception) {
            log.error("Error processing message from C9 id: {} - type: {}", c9.id, c9.type, e)
            throw e
        }
    }
}
